@import "tailwindcss";

@import "flowbite/src/themes/default";
@import "tw-animate-css";

@plugin "flowbite/plugin";
@plugin "vidstack/tailwind.cjs" {
  webComponents: true;
  prefix: 'media';
}

@source "../../../node_modules/flowbite";

@theme {
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  
  /* Media player theme variables */
  --color-media-brand: rgb(245 245 245);
  --color-media-focus: rgb(78 156 246);
  
  --font-sans: 'Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'system-ui', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-body: 'Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'system-ui', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-mono: 'ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace';
}

/* Custom variants from Vidstack example - Tailwind v4 syntax */
@variant hocus (&:hover, &:focus-visible);
@variant group-hocus (.group:hover &, .group:focus-visible &);
@variant parent-data (.parent[data-*] > &);

@layer base {
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
}