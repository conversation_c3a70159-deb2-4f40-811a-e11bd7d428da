<%= turbo_frame_tag generation_task_placeholder_id(task, song_index) do %>
  <li class="group py-3 px-4" id="generation_task_<%= task.id %>_song_<%= song_index %>">
    <div class="flex items-center">
      <!-- Cover Placeholder for Failed -->
      <div class="shrink-0">
        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
          <%= flowbite_icon('exclamation-circle-outline', class: 'size-6 text-red-600') %>
        </div>
      </div>
      <!-- Song Info -->
      <div class="flex-1 min-w-0 mx-4">
        <p class="text-sm font-medium text-gray-900 truncate">
          <%= generation_task_placeholder_title(task) %>
        </p>
        <p class="text-sm text-gray-500 truncate">
          <%= generation_task_song_variant_info(task, song_index) %> • 
          <% if task.error_data&.dig("message").present? %>
            <%= task.error_data["message"] %>
          <% else %>
            Generation failed
          <% end %>
        </p>
      </div>
      <!-- Status and Actions -->
      <div class="inline-flex items-center space-x-2">
        <!-- Failed Status Badge -->
        <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <%= flowbite_icon('exclamation-circle-outline', class: 'size-3 mr-1') %>
          Failed
        </div>
        <!-- Delete Button -->
        <%= button_to generation_path(task), 
                  class: "inline-flex items-center p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-red-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all",
                  method: :delete,
                  title: "Delete failed generation" do %>
          <%= flowbite_icon('trash-bin-outline', class: 'size-3.5') %>
        <% end %>
      </div>
    </div>
  </li>
<% end %> 