<% if song.present? %>
  <%= turbo_frame_tag dom_id(local_assigns[:song] || Song.new, :player_favorite_button) do %>
    <%= button_to toggle_favorite_song_path(song),
                    method: :patch,
                    class: "p-1.5 rounded-lg hover:bg-purple-50 dark:hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-200 dark:focus:ring-purple-800" do %>
      <%= flowbite_icon(song.favorite? ? 'heart-solid' : 'heart-outline', class: "size-4 #{song.favorite? ? 'text-red-500 dark:text-red-400' : 'text-gray-600 dark:text-gray-300 hover:text-red-500 dark:hover:text-red-400'}") %>
      <span class="sr-only">
        <%= song.favorite? ? 'Liked' : 'Like' %>
      </span>
    <% end %>
  <% end %>
<% else %>
  <%= turbo_frame_tag dom_id(Song.new, :player_favorite_button) do %>
    <button class="cursor-not-allowed p-1.5 rounded-lg opacity-50 transition-colors" title="No song selected" disabled>
      <%= flowbite_icon('heart-outline', class: 'size-4 text-gray-400') %>
    </button>
  <% end %>
<% end %>