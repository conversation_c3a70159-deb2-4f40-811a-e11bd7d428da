<% content_for :title, "My Songs" %>
<%# Subscribe to Turbo Streams for generation task updates %>
<%= turbo_stream_from Current.user, :generation_task_updates %>
<div class="min-h-screen bg-gradient-to-br from-purple-50/30 via-pink-50/20 to-red-50/30 dark:from-gray-900/50 dark:via-gray-800/30 dark:to-gray-800/50 flex flex-col pb-24 px-8 transition-all duration-200">
  <!-- Header Section -->
  <div class="border-b border-purple-200/30 dark:border-gray-700/50">
    <div class="pb-3 px-2">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-semibold bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 bg-clip-text text-transparent dark:from-purple-400 dark:via-pink-400 dark:to-red-400">My Songs</h1>
          <p class="text-gray-600 dark:text-gray-300 mt-1 text-sm">Generate, manage, and explore your AI-created music</p>
        </div>
      </div>
    </div>
  </div>
  <!-- Three-Column Layout with bottom padding for floating player -->
  <div class="self-center w-full flex-1 max-w-7xl py-6 @container">
    <div class="grid grid-cols-1 @2xl:grid-cols-[minmax(24rem,1fr)_1fr] @5xl:grid-cols-[minmax(24rem,1fr)_1fr_1fr] gap-6 flex-1">
      <!-- Left Column: Generation Form -->
      <div class="@5xl:col-span-1">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-purple-200/30 dark:border-gray-700 overflow-hidden h-full flex flex-col transition-all duration-200 hover:shadow-md">
          <div class="p-4 border-b border-purple-200/30 dark:border-gray-700 flex-shrink-0">
            <h2 class="text-lg font-semibold bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 bg-clip-text text-transparent dark:from-purple-400 dark:via-pink-400 dark:to-red-400">Generate Music</h2>
            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Create new AI-powered music</p>
          </div>
          <%= turbo_frame_tag "generation_form", src: new_generation_path, class: "flex-1 flex flex-col" do %>
            <div class="p-6 text-center flex-1 flex flex-col justify-center">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 dark:border-purple-400 mx-auto"></div>
              <p class="text-gray-500 dark:text-gray-400 mt-2">Loading generation form...</p>
            </div>
          <% end %>
        </div>
      </div>
      <!-- Middle Column: Song List -->
      <div class="@5xl:col-span-1">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-purple-200/30 dark:border-gray-700 overflow-hidden h-full flex flex-col transition-all duration-200 hover:shadow-md">
          <div class="p-4 border-b border-purple-200/30 dark:border-gray-700 flex-shrink-0">
            <h2 class="text-lg font-semibold bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 bg-clip-text text-transparent dark:from-purple-400 dark:via-pink-400 dark:to-red-400">My Songs</h2>
            <%= render "songs/song_stats" %>
          </div>
          <%= turbo_frame_tag "song_list_frame", class: "flex-1 flex flex-col" do %>
            <%= render "search_header" %>
            <div class="flex-1 overflow-y-auto flex flex-col">
              <%= render "song_list", songs: @songs, generating_tasks: @generating_tasks, failed_tasks: @failed_tasks %>
            </div>
            <%= render "pagination_footer" if @total_pages > 1 %>
          <% end %>
        </div>
      </div>
      <!-- Right Column: Song Details -->
      <div class="col-span-1 @2xl:col-span-2 @5xl:col-span-1">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-purple-200/30 dark:border-gray-700 overflow-hidden h-full flex flex-col transition-all duration-200 hover:shadow-md">
          <div class="p-4 border-b border-purple-200/30 dark:border-gray-700 flex-shrink-0">
            <h2 class="text-lg font-semibold bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 bg-clip-text text-transparent dark:from-purple-400 dark:via-pink-400 dark:to-red-400">Song Details</h2>
            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Select a song to view details</p>
          </div>
          <%= turbo_frame_tag "song_details", class: "flex-1 flex flex-col" do %>
            <%= render "empty_song" %>
          <% end %>
        </div>
      </div>
    </div>
  </div>
  <!-- Persistent/Floating Media Player -->
  <div class="fixed bottom-0 left-0 right-0 md:left-64 z-20 transition-all duration-300 ease-in-out">
    <!-- Main player container -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <!-- Player component -->
      <%= turbo_frame_tag "song_player", class: "mt-2" do %>
        <%= render "player" %>
      <% end %>
    </div>
  </div>
</div>