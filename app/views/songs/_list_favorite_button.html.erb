<%= turbo_frame_tag dom_id(song, :list_favorite_button) do %>
  <%= button_to toggle_favorite_song_path(song),
                      method: :patch,
                      class: "inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center rounded-md hover:bg-gray-100 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all #{song.favorite ? 'text-red-600 opacity-100' : 'text-gray-400 pointer-coarse:opacity-100 pointer-fine:opacity-0 pointer-fine:group-hover:opacity-100'}" do %>
    <%= flowbite_icon(song.favorite ? 'heart-solid' : 'heart-outline', class: "size-3.5") %>
    <span class="sr-only">
      <%= song.favorite ? 'Liked' : 'Like' %>
    </span>
  <% end %>
<% end %>