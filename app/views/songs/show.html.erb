<% content_for :title, @song.title %>
<div class="min-h-screen bg-gray-50">
  <!-- Header Section -->
  <div class="bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Song Details</h1>
          <p class="text-gray-600 mt-1">View and manage your generated song</p>
        </div>
        <div class="flex space-x-3">
          <%= link_to songs_path,
              class: "text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5" do %>
            <%= flowbite_icon('arrow-left-solid', class: 'w-4 h-4 mr-2') %>
            Back to Songs
          <% end %>
        </div>
      </div>
    </div>
  </div>
  <!-- Main Content -->
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Song Card -->
    <div class="w-full max-w-sm bg-white border border-gray-200 rounded-lg shadow-sm mx-auto">
      <%= turbo_frame_tag "song_details" do %>
        <%= render "song_info", song: @song %>
      <% end %>
    </div>
  </div>
  <%= turbo_frame_tag "song_player" do %>
    <%= render "player", song: @song %>
  <% end %>
</div>